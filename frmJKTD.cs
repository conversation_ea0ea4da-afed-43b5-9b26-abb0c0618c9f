using AIServices.Models.Chat;
using AIServices.Utils;
using DevExpress.DataAccess.Native.Json;
using Microsoft.CSharp.RuntimeBinder;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HIH.CRM.Import.AIOCR
{
    public partial class frmJKTD : HIH.Framework.BaseUIDX.BaseCRMForm
    {
        private readonly TaskSequenceHelper task;
        private string currentPdfPath;
        private string fileId;
        private DataTable dataTable; // 添加DataTable作为类成员变量

        // 定义字段名称数组，只需要在这里维护一次
        private readonly string[] fieldNames = { "分单号", "总单号", "船名航次", "起运港", "目的港", "件数", "体积", "毛重", "离港日期" };

        public frmJKTD()
        {
            InitializeComponent();
            isLoadPerm = false;
            task = new TaskSequenceHelper("414242d05ce04f8f87b811dc7f42aa1c");
            InitializeGridData(); // 初始化GridView数据
        }

        private void InitializeGridData()
        {
            // 创建DataTable并添加预设的字段名称
            dataTable = new DataTable();
            dataTable.Columns.Add("key", typeof(string));
            dataTable.Columns.Add("value", typeof(string));

            // 使用字段名称数组添加预设的字段行
            foreach (string fieldName in fieldNames)
            {
                DataRow row = dataTable.NewRow();
                row["key"] = fieldName;
                row["value"] = ""; // 初始值为空
                dataTable.Rows.Add(row);
            }

            // 绑定到GridView
            gd.DataSource = dataTable;
            gdv.BestFitColumns();
        }

        private void btnSelectFile_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gdv.RefreshData();
            return;
            //选择PDF文件
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "PDF文件|*.pdf",
                Title = "选择PDF文件"
            };
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                fileId = "";
                //在PDFview打开
                currentPdfPath = openFileDialog.FileName;
                pdfView.LoadDocument(openFileDialog.FileName);
            }

        }

        private async void btnStartOCR_ItemClickAsync(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (string.IsNullOrEmpty(currentPdfPath))
            {
                ICF.ISD.ShowError("请先选择PDF文件！");
                return;
            }
            try
            {
                if (string.IsNullOrEmpty(fileId))
                {
                    using (FileStream fileStream = File.OpenRead(currentPdfPath))
                    {
                        string fileName = Path.GetFileName(currentPdfPath);
                        fileId = await task.UploadFileAsync(fileStream, fileName);
                        //已经获取到fileID,将获取到的fileId，查询状态
                        string status = await task.PollFileStatusWithTimeoutAsync(fileId, 180);
                        if (status != "FILE_IS_READY")
                        {
                            ICF.ISD.ShowError("文件解析失败，状态：" + status + "！");
                            return;
                        }
                    }
                }

                //请求AI接口
                string message = "{\"version\":\"1.1\",\"description\":\"结构化文档解析规则\",\"fields\":{\"分单号\":{\"description\":\"分单号，可能带FRA前缀，不会是HHJK前缀\",\"extraction_steps\":[{\"step\":1,\"action\":\"优先匹配关键词\",\"target\":[\"SEA WAYBILL No\",\"HAWB No\",\"B/L NO\"],\"cleanup\":{\"remove\":[\" \",\"-\"],\"keep_prefix\":\"FRA\"}},{\"step\":2,\"action\":\"匹配FRA及后续内容\",\"pattern\":\"FRA[\\w\\-\\/\\s]+\",\"on_match\":\"full_value\"},{\"step\":3,\"action\":\"匹配无FRA的普通分单号\",\"pattern\":\"[A-Z0-9\\-]{5,}\",\"on_match\":\"full_value\"}],\"output_rules\":{\"start\":\"如果未找到分单号信息，请理解文档来选择正确的单号提取。\",\"empty_value\":\"\"}},\"总单号\":{\"description\":\"总单号，不会是HHJK前缀\",\"source_priority\":[\"MAWB\",\"MAWB#\",\"MASTER B/L\",\"MBL\",\"MASTER\"],\"extraction_logic\":{\"positional_rules\":[{\"condition\":\"分单号_exists_and_starts_with_FRA\",\"extract_right_of\":\"分单号\"}]},\"processing\":{\"remove_chars\":[\"-\",\" \"],\"to_uppercase\":true},\"empty_value\":\"\"},\"船名航次\":{\"source_priority\":[\"VESSEL AND VOYAGE NO\",\"Requested Flight\",\"DHL\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"remove_suffixes\":[\"日期\",\"ETA\",\"ETD\",\"\\d{4}-\\d{2}-\\d{2}\"]},\"output_rules\":{\"start\":\"提取船名或者航次,不需要带航空日期后缀,保留空格部分\",\"empty_value\":\"\"}},\"起运港\":{\"source_priority\":[\"PLACE AND DATE OF ISSUE\",\"Airport of Departure\",\"ALBERT-SCHWEITZER-STRASSE\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"目的港\":{\"source_priority\":[\"PORT OF DISCHARGE\",\"TO\",\"DESTINATION CODE\",\"Airport of Destination\"],\"processing\":{\"regex\":\"[\\w\\-\\/\\s]+\",\"split_by\":[\",\",\"/\"],\"take_part\":0,\"remove_words\":[\"PORT\",\"AIRPORT\",\"APT\",\"TO\"]},\"output_rules\":{\"start\":\"如果提取到是城市，查看文档内容是否有机场代码，返回机场代码。\",\"empty_value\":\"\"}},\"件数\":{\"source_priority\":[\"Total Items\",\"Pallet(s)\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":0},\"体积\":{\"source_priority\":[\"体积字段\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"毛重\":{\"source_priority\":[\"Gross Cargo Weight\"],\"processing\":{\"regex\":\"[\\d\\.]+\",\"extract_numbers\":true,\"remove_units\":true},\"empty_value\":\"\"},\"离港日期\":{\"description\": \"离港日期、也叫 Arrive Date\",\"source_priority\":[\"SHIPPED ON BOARD DATE\",\"Arrive Date\"],\"processing\":{\"regex\":\"[\\d\\-/\\.]+\",\"date_formats\":[\"dd/mm/yyyy\",\"mm-dd-yyyy\",\"yyyy-mm-dd\",\"yyyy/MM/dd\"],\"output_format\":\"yyyy-MM-dd\"},\"empty_value\":\"\"}},\"global_rules\":{\"numeric_default\":0,\"string_default\":\"\",\"date_default\":\"\",\"strict_mode\":false}}";

                ChatRequest chatRequest = new ChatRequest();
                chatRequest.Question = message;
                chatRequest.FileId = new string[] { fileId };
                chatRequest.Temperature = 0.25;
                //请求接口
                var result = await task.ProcessSingleRequestsAsync(chatRequest);
                BindDynamicToGridView(result);
            }
            catch (Exception exc)
            {
                ICF.ISD.ShowError(exc.Message);
            }








        }



        private void BindDynamicToGridView(dynamic jsonData)
        {
            try
            {
                // 调试：查看jsonData的实际类型
                System.Diagnostics.Debug.WriteLine($"jsonData类型: {jsonData.GetType()}");
                System.Diagnostics.Debug.WriteLine($"jsonData内容: {jsonData}");

                // 测试直接访问属性
                try
                {
                    var test = jsonData.分单号;
                    System.Diagnostics.Debug.WriteLine($"直接访问 jsonData.分单号: {test}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"直接访问失败: {ex.Message}");
                }

                // 遍历字段名称数组，动态获取对应的属性值
                foreach (string fieldName in fieldNames)
                {
                    try
                    {
                        // 使用反射获取dynamic对象的属性值
                        var value = GetDynamicProperty(jsonData, fieldName);
                        System.Diagnostics.Debug.WriteLine($"字段 {fieldName} 的值: {value}");
                        UpdateFieldValue(fieldName, value);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取字段 {fieldName} 时发生错误: {ex.Message}");
                        UpdateFieldValue(fieldName, ""); // 设置为空值
                    }
                }

                gd.DataSource = dataTable;
                gdv.BestFitColumns(); // 自动调整列宽
            }
            catch (Exception ex)
            {
                ICF.ISD.ShowError($"绑定数据到GridView时发生错误: {ex.Message}");
            }
        }

        private dynamic GetDynamicProperty(dynamic obj, string propertyName)
        {
            try
            {
                // 对于dynamic对象，尝试将其转换为字典形式访问
                if (obj is IDictionary<string, object> dict)
                {
                    return dict.ContainsKey(propertyName) ? dict[propertyName] : null;
                }

                // 如果是ExpandoObject类型
                if (obj is System.Dynamic.ExpandoObject expando)
                {
                    var expandoDict = (IDictionary<string, object>)expando;
                    return expandoDict.ContainsKey(propertyName) ? expandoDict[propertyName] : null;
                }

                // 尝试使用反射获取属性值（适用于匿名对象等）
                Type objType = obj.GetType();
                PropertyInfo property = objType.GetProperty(propertyName);
                if (property != null)
                {
                    return property.GetValue(obj);
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        private void UpdateFieldValue(string fieldName, dynamic value)
        {
            try
            {
                // 在DataTable中查找对应的行并更新值
                foreach (DataRow row in dataTable.Rows)
                {
                    if (row["key"].ToString() == fieldName)
                    {
                        row["value"] = value?.ToString() ?? "";
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果更新失败，记录错误但不中断整个过程
                System.Diagnostics.Debug.WriteLine($"更新字段 {fieldName} 时发生错误: {ex.Message}");
            }
        }


    }
}
